"""
Gaia Ceto v2 - Simplified Chat System

A clean, minimal implementation of chat functionality without the complexity
of the original Django/MCP architecture.

Architecture:
- core/ - Pure business logic with no framework dependencies
- interfaces/ - Protocol adapters (Django, MCP, HTTP, etc.)

This separation ensures the core business logic remains framework-agnostic
and can be used by any interface layer.
"""

__version__ = "2.0.0"
__author__ = "Gaia Development Team"

# Import core business logic (selective imports to avoid deleted storage classes)
from gaia_ceto_v2.core import (
    Conversation, Message, create_conversation, conversation_summary,
    LLMProvider, MockLLM, OpenAILLM, AnthropicLLM, LiteLLM,
    create_llm_provider, get_available_providers, provider_requires_api_key,
    ChatManager, create_chat_manager
)

# Re-export everything from core for backward compatibility
__all__ = [
    # Data models
    'Conversation', 'Message', 'create_conversation', 'conversation_summary',
    
    # LLM providers
    'LLMProvider', 'MockLLM', 'OpenAILLM', 'AnthropicLLM',
    'create_llm_provider', 'get_available_providers', 'provider_requires_api_key',
    
    # Storage backends (updated to new architecture)
    # Use: FileConversationRepository, ConversationRepository
    # Import from: core.file_storage, core.storage_interface
    
    # Core orchestration
    'ChatManager', 'create_chat_manager'
]
